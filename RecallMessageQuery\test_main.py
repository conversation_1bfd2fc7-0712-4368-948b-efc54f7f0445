import asyncio
import sqlite3
import os
from datetime import datetime, timed<PERSON>ta
from typing import List, Optional

from loguru import logger
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase


class RecallMessageQuery(PluginBase):
    """
    撤回消息查询插件 - 简化测试版本
    """

    description = "撤回消息查询插件"
    author = "AI助手"
    version = "2.0.0"

    def __init__(self):
        super().__init__()
        
        logger.info("RecallMessageQuery 插件初始化开始...")
        
        # 使用默认配置
        self.enable = True
        self.trigger_words = ["撤回了啥", "撤回的什么", "撤回了什么", "看看撤回", "撤回查询"]
        self.max_recall_messages = 50
        self.save_days = 7
        self.show_recall_time = True
        self.show_recaller_info = True

        # 数据库配置
        self.db_file = "recall_messages.db"
        self.db_connection = None
        self.initialize_database()
        
        logger.info("RecallMessageQuery 插件初始化完成")

    def initialize_database(self):
        """初始化数据库连接"""
        try:
            self.db_connection = sqlite3.connect(self.db_file)
            self.create_recall_table()
            logger.info("撤回消息数据库连接已建立")
        except Exception as e:
            logger.error(f"初始化撤回消息数据库失败: {e}")

    def create_recall_table(self):
        """创建撤回消息表"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS recall_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id TEXT NOT NULL,
                    original_msg_id TEXT NOT NULL,
                    sender_wxid TEXT NOT NULL,
                    sender_nickname TEXT,
                    original_content TEXT NOT NULL,
                    original_msg_type INTEGER,
                    recall_time INTEGER NOT NULL,
                    recaller_wxid TEXT,
                    recaller_nickname TEXT,
                    created_at INTEGER NOT NULL
                )
            """)
            
            self.db_connection.commit()
            logger.info("撤回消息表创建成功")
        except sqlite3.Error as e:
            logger.error(f"创建撤回消息表失败：{e}")

    @on_text_message(priority=50)
    async def handle_text_message(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return True
            
        try:
            content = message.get("Content", "")
            chat_id = message.get("FromWxid", "")
            
            # 检查是否为查询撤回消息的命令
            if any(trigger in content for trigger in self.trigger_words):
                logger.info(f"收到撤回查询命令: {content}")
                await bot.send_text_message(chat_id, "撤回消息查询插件正在工作！🎉\n\n目前是测试版本，功能正在完善中...")
                return False  # 阻止后续插件处理
                
        except Exception as e:
            logger.error(f"处理文本消息时出错: {e}")
            
        return True

    async def close(self):
        """插件关闭时的清理工作"""
        try:
            if self.db_connection:
                self.db_connection.close()
                logger.info("撤回消息数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭撤回消息插件时出错: {e}")
