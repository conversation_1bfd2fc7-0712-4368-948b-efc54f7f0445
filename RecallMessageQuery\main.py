import asyncio
import json
import re
import tomllib
import xml.etree.ElementTree as ET
from collections import defaultdict, deque
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import sqlite3
import os

from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, on_xml_message
from utils.plugin_base import PluginBase


class RecallMessageQuery(PluginBase):
    """
    撤回消息查询插件 - 记录和查询被撤回的消息内容
    """

    description = "撤回消息查询插件"
    author = "AI助手"
    version = "1.0.0"

    def __init__(self):
        super().__init__()

        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")

        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)

            # 读取基本配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", False)
            self.priority = basic_config.get("priority", 60)

            # 读取插件配置
            plugin_config = config.get("RecallMessageQuery", {})
            self.trigger_words = plugin_config.get("trigger_words", ["撤回了啥", "撤回的什么"])
            self.max_recall_messages = plugin_config.get("max_recall_messages", 50)
            self.save_days = plugin_config.get("save_days", 7)
            self.show_recall_time = plugin_config.get("show_recall_time", True)
            self.show_recaller_info = plugin_config.get("show_recaller_info", True)

            logger.info("RecallMessageQuery 插件配置加载成功")
        except Exception as e:
            logger.error(f"加载RecallMessageQuery配置文件失败: {str(e)}")
            self.enable = False

        # 内存中存储最近的消息，用于匹配撤回的消息
        self.recent_messages: Dict[str, deque] = defaultdict(lambda: deque(maxlen=100))

        # 数据库配置
        self.db_file = "recall_messages.db"
        self.db_connection = None
        self.initialize_database()

    def initialize_database(self):
        """初始化数据库连接"""
        try:
            self.db_connection = sqlite3.connect(self.db_file)
            self.create_recall_table()
            logger.info("撤回消息数据库连接已建立")
        except Exception as e:
            logger.error(f"初始化撤回消息数据库失败: {e}")

    def create_recall_table(self):
        """创建撤回消息表"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS recall_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id TEXT NOT NULL,
                    original_msg_id TEXT NOT NULL,
                    sender_wxid TEXT NOT NULL,
                    sender_nickname TEXT,
                    original_content TEXT NOT NULL,
                    original_msg_type INTEGER,
                    recall_time INTEGER NOT NULL,
                    recaller_wxid TEXT,
                    recaller_nickname TEXT,
                    created_at INTEGER NOT NULL
                )
            """)

            # 创建索引
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_chat_recall_time
                ON recall_messages(chat_id, recall_time DESC)
            """)

            self.db_connection.commit()
            logger.info("撤回消息表创建成功")
        except sqlite3.Error as e:
            logger.error(f"创建撤回消息表失败：{e}")

    @on_text_message(priority=50)
    async def handle_text_message(self, bot: WechatAPIClient, message: dict):
        """处理文本消息，存储到内存中以备撤回时查询"""
        if not self.enable:
            return True

        try:
            chat_id = message.get("FromWxid", "")
            msg_id = message.get("MsgId", "")
            sender_wxid = message.get("SenderWxid", "")
            content = message.get("Content", "")
            create_time = message.get("CreateTime", 0)

            # 存储消息到内存中
            msg_data = {
                "msg_id": msg_id,
                "sender_wxid": sender_wxid,
                "content": content,
                "msg_type": 1,  # 文本消息
                "create_time": create_time
            }

            self.recent_messages[chat_id].append(msg_data)

            # 检查是否为查询撤回消息的命令
            if any(trigger in content for trigger in self.trigger_words):
                await self.handle_recall_query(bot, message)
                return False  # 阻止后续插件处理

        except Exception as e:
            logger.error(f"处理文本消息时出错: {e}")

        return True

    @on_xml_message(priority=80)
    async def handle_xml_message(self, bot: WechatAPIClient, message: dict):
        """处理XML消息，检测撤回消息"""
        if not self.enable:
            return True

        try:
            content = message.get("Content", "")

            # 检查是否为撤回消息
            if "revokemsg" in content and "sysmsg" in content:
                await self.handle_recall_message(bot, message, content)

        except Exception as e:
            logger.error(f"处理XML消息时出错: {e}")

        return True

    async def handle_recall_message(self, bot: WechatAPIClient, message: dict, xml_content: str):
        """处理撤回消息"""
        try:
            # 解析XML内容
            recall_info = self.parse_recall_xml(xml_content)
            if not recall_info:
                return

            chat_id = message.get("FromWxid", "")
            recall_time = message.get("CreateTime", 0)
            recaller_wxid = message.get("SenderWxid", "")

            # 从内存中查找被撤回的原始消息
            original_msg = self.find_original_message(chat_id, recall_info["msgid"])

            if original_msg:
                # 获取发送者和撤回者的昵称
                sender_nickname = await self.get_user_nickname(bot, original_msg["sender_wxid"])
                recaller_nickname = await self.get_user_nickname(bot, recaller_wxid)

                # 保存撤回消息到数据库
                await self.save_recall_message(
                    chat_id=chat_id,
                    original_msg_id=recall_info["msgid"],
                    sender_wxid=original_msg["sender_wxid"],
                    sender_nickname=sender_nickname,
                    original_content=original_msg["content"],
                    original_msg_type=original_msg["msg_type"],
                    recall_time=recall_time,
                    recaller_wxid=recaller_wxid,
                    recaller_nickname=recaller_nickname
                )

                logger.info(f"记录撤回消息: {sender_nickname} 的消息被 {recaller_nickname} 撤回")
            else:
                logger.warning(f"未找到被撤回的原始消息，消息ID: {recall_info['msgid']}")

        except Exception as e:
            logger.error(f"处理撤回消息时出错: {e}")

    def parse_recall_xml(self, xml_content: str) -> Optional[dict]:
        """解析撤回消息的XML内容"""
        try:
            # 清理XML内容
            xml_content = xml_content.strip()

            # 移除可能的用户名前缀（如 "flyhunterl:\n"）
            if ":\n" in xml_content and xml_content.index(":\n") < 50:
                xml_content = xml_content.split(":\n", 1)[1]

            root = ET.fromstring(xml_content)
            revoke_elem = root.find(".//revokemsg")

            if revoke_elem is not None:
                return {
                    "session": revoke_elem.find("session").text if revoke_elem.find("session") is not None else "",
                    "msgid": revoke_elem.find("msgid").text if revoke_elem.find("msgid") is not None else "",
                    "newmsgid": revoke_elem.find("newmsgid").text if revoke_elem.find("newmsgid") is not None else "",
                    "replacemsg": revoke_elem.find("replacemsg").text if revoke_elem.find("replacemsg") is not None else ""
                }
        except Exception as e:
            logger.error(f"解析撤回消息XML失败: {e}")
            logger.debug(f"XML内容: {xml_content}")

        return None

    def find_original_message(self, chat_id: str, msg_id: str) -> Optional[dict]:
        """从内存中查找被撤回的原始消息"""
        try:
            if chat_id in self.recent_messages:
                for msg in self.recent_messages[chat_id]:
                    if str(msg["msg_id"]) == str(msg_id):
                        return msg
        except Exception as e:
            logger.error(f"查找原始消息时出错: {e}")

        return None

    async def get_user_nickname(self, bot: WechatAPIClient, wxid: str) -> str:
        """获取用户昵称"""
        try:
            if hasattr(bot, 'get_nickname'):
                nickname = await bot.get_nickname(wxid)
                return nickname if nickname else wxid
        except Exception as e:
            logger.debug(f"获取用户 {wxid} 昵称失败: {e}")

        return wxid

    async def save_recall_message(self, chat_id: str, original_msg_id: str, sender_wxid: str,
                                sender_nickname: str, original_content: str, original_msg_type: int,
                                recall_time: int, recaller_wxid: str, recaller_nickname: str):
        """保存撤回消息到数据库"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                INSERT INTO recall_messages
                (chat_id, original_msg_id, sender_wxid, sender_nickname, original_content,
                 original_msg_type, recall_time, recaller_wxid, recaller_nickname, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                chat_id, original_msg_id, sender_wxid, sender_nickname, original_content,
                original_msg_type, recall_time, recaller_wxid, recaller_nickname,
                int(datetime.now().timestamp())
            ))
            self.db_connection.commit()

            # 清理过期的撤回消息
            await self.cleanup_old_recalls(chat_id)

        except sqlite3.Error as e:
            logger.error(f"保存撤回消息失败: {e}")

    async def cleanup_old_recalls(self, chat_id: str):
        """清理过期的撤回消息"""
        try:
            cutoff_time = datetime.now() - timedelta(days=self.save_days)
            cutoff_timestamp = int(cutoff_time.timestamp())

            cursor = self.db_connection.cursor()
            cursor.execute("""
                DELETE FROM recall_messages
                WHERE chat_id = ? AND recall_time < ?
            """, (chat_id, cutoff_timestamp))

            # 限制每个聊天的撤回消息数量
            cursor.execute("""
                DELETE FROM recall_messages
                WHERE chat_id = ? AND id NOT IN (
                    SELECT id FROM recall_messages
                    WHERE chat_id = ?
                    ORDER BY recall_time DESC
                    LIMIT ?
                )
            """, (chat_id, chat_id, self.max_recall_messages))

            self.db_connection.commit()

        except sqlite3.Error as e:
            logger.error(f"清理过期撤回消息失败: {e}")

    async def handle_recall_query(self, bot: WechatAPIClient, message: dict):
        """处理撤回消息查询请求"""
        try:
            chat_id = message.get("FromWxid", "")

            # 从数据库获取最近的撤回消息
            recall_messages = await self.get_recent_recalls(chat_id, limit=10)

            if not recall_messages:
                await bot.send_text_message(chat_id, "最近没有撤回的消息记录。")
                return

            # 构建回复消息
            reply_text = "📝 最近的撤回消息记录：\n\n"

            for i, recall in enumerate(recall_messages, 1):
                sender_name = recall["sender_nickname"] or recall["sender_wxid"]
                content = recall["original_content"]

                # 限制内容长度
                if len(content) > 50:
                    content = content[:50] + "..."

                reply_text += f"{i}. {sender_name}: {content}\n"

                if self.show_recall_time:
                    recall_time = datetime.fromtimestamp(recall["recall_time"])
                    reply_text += f"   ⏰ 撤回时间: {recall_time.strftime('%m-%d %H:%M:%S')}\n"

                if self.show_recaller_info and recall["recaller_nickname"]:
                    recaller_name = recall["recaller_nickname"] or recall["recaller_wxid"]
                    if recaller_name != sender_name:
                        reply_text += f"   👤 撤回者: {recaller_name}\n"

                reply_text += "\n"

            reply_text += "💡 提示：只显示最近10条撤回记录"

            await bot.send_text_message(chat_id, reply_text)

        except Exception as e:
            logger.error(f"处理撤回消息查询时出错: {e}")
            await bot.send_text_message(chat_id, "查询撤回消息时出现错误，请稍后重试。")

    async def get_recent_recalls(self, chat_id: str, limit: int = 10) -> List[dict]:
        """获取最近的撤回消息"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                SELECT sender_wxid, sender_nickname, original_content, original_msg_type,
                       recall_time, recaller_wxid, recaller_nickname
                FROM recall_messages
                WHERE chat_id = ?
                ORDER BY recall_time DESC
                LIMIT ?
            """, (chat_id, limit))

            rows = cursor.fetchall()
            recalls = []

            for row in rows:
                recalls.append({
                    "sender_wxid": row[0],
                    "sender_nickname": row[1],
                    "original_content": row[2],
                    "original_msg_type": row[3],
                    "recall_time": row[4],
                    "recaller_wxid": row[5],
                    "recaller_nickname": row[6]
                })

            return recalls

        except sqlite3.Error as e:
            logger.error(f"获取撤回消息失败: {e}")
            return []

    async def close(self):
        """插件关闭时的清理工作"""
        try:
            if self.db_connection:
                self.db_connection.close()
                logger.info("撤回消息数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭撤回消息插件时出错: {e}")