import asyncio
import json
import re
import xml.etree.ElementTree as ET
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import sqlite3
import os

from loguru import logger
import tomllib

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class RecallMessageQuery(PluginBase):
    """
    撤回消息查询插件 - 记录和查询被撤回的消息内容
    """

    description = "撤回消息查询插件"
    author = "AI助手"
    version = "2.0.0"

    def __init__(self):
        super().__init__()

        logger.info("开始初始化 RecallMessageQuery 插件...")

        # 获取配置文件路径
        config_path = os.path.join(os.path.dirname(__file__), "config.toml")

        try:
            with open(config_path, "rb") as f:
                config = tomllib.load(f)

            # 读取基本配置
            basic_config = config.get("basic", {})
            self.enable = basic_config.get("enable", True)  # 默认启用
            self.priority = basic_config.get("priority", 60)

            # 读取插件配置
            plugin_config = config.get("RecallMessageQuery", {})
            self.trigger_words = plugin_config.get("trigger_words", ["撤回了啥", "撤回的什么"])
            self.max_recall_messages = plugin_config.get("max_recall_messages", 50)
            self.save_days = plugin_config.get("save_days", 7)
            self.show_recall_time = plugin_config.get("show_recall_time", True)
            self.show_recaller_info = plugin_config.get("show_recaller_info", True)

            logger.info(f"RecallMessageQuery 插件配置加载成功，启用状态: {self.enable}")
            logger.info(f"触发词: {self.trigger_words}")
        except Exception as e:
            logger.error(f"加载RecallMessageQuery配置文件失败: {str(e)}")
            # 即使配置加载失败，也使用默认配置
            self.enable = True
            self.trigger_words = ["撤回了啥", "撤回的什么"]
            self.max_recall_messages = 50
            self.save_days = 7
            self.show_recall_time = True
            self.show_recaller_info = True
            logger.info("使用默认配置继续运行")

        # 数据库配置
        self.db_file = "recall_messages.db"
        self.db_connection = None
        self.initialize_database()

        logger.info("RecallMessageQuery 插件初始化完成")

    def initialize_database(self):
        """初始化数据库连接"""
        try:
            self.db_connection = sqlite3.connect(self.db_file)
            self.create_recall_table()
            logger.info("撤回消息数据库连接已建立")
        except Exception as e:
            logger.error(f"初始化撤回消息数据库失败: {e}")

    def create_recall_table(self):
        """创建撤回消息表"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS recall_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    chat_id TEXT NOT NULL,
                    original_msg_id TEXT NOT NULL,
                    sender_wxid TEXT NOT NULL,
                    sender_nickname TEXT,
                    original_content TEXT NOT NULL,
                    original_msg_type INTEGER,
                    recall_time INTEGER NOT NULL,
                    recaller_wxid TEXT,
                    recaller_nickname TEXT,
                    created_at INTEGER NOT NULL
                )
            """)

            # 创建索引
            cursor.execute("""
                CREATE INDEX IF NOT EXISTS idx_chat_recall_time
                ON recall_messages(chat_id, recall_time DESC)
            """)

            self.db_connection.commit()
            logger.info("撤回消息表创建成功")
        except sqlite3.Error as e:
            logger.error(f"创建撤回消息表失败：{e}")

    def get_table_name(self, chat_id: str) -> str:
        """
        生成表名，将chat_id中的特殊字符替换掉，避免SQL注入和表名错误
        """
        return "chat_" + re.sub(r"[^a-zA-Z0-9_]", "_", chat_id)

    def create_message_table_if_not_exists(self, chat_id: str):
        """为每个chat_id创建一个单独的消息表"""
        table_name = self.get_table_name(chat_id)
        cursor = self.db_connection.cursor()
        try:
            cursor.execute(f"""
                CREATE TABLE IF NOT EXISTS "{table_name}" (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    msg_id TEXT NOT NULL,
                    sender_wxid TEXT NOT NULL,
                    create_time INTEGER NOT NULL,
                    content TEXT NOT NULL,
                    msg_type INTEGER DEFAULT 1
                )
            """)

            # 创建索引
            cursor.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_{table_name}_msg_id
                ON "{table_name}"(msg_id)
            """)

            cursor.execute(f"""
                CREATE INDEX IF NOT EXISTS idx_{table_name}_create_time
                ON "{table_name}"(create_time DESC)
            """)

            self.db_connection.commit()
            logger.debug(f"消息表 {table_name} 创建成功")
        except sqlite3.Error as e:
            logger.error(f"创建消息表 {table_name} 失败：{e}")

    def save_message_to_db(self, chat_id: str, msg_id: str, sender_wxid: str, create_time: int, content: str, msg_type: int = 1):
        """将消息保存到数据库"""
        table_name = self.get_table_name(chat_id)
        try:
            cursor = self.db_connection.cursor()
            cursor.execute(f"""
                INSERT INTO "{table_name}" (msg_id, sender_wxid, create_time, content, msg_type)
                VALUES (?, ?, ?, ?, ?)
            """, (msg_id, sender_wxid, create_time, content, msg_type))
            self.db_connection.commit()
            logger.debug(f"消息保存到表 {table_name}: msg_id={msg_id}, sender_wxid={sender_wxid}")
        except sqlite3.Error as e:
            logger.error(f"保存消息到表 {table_name} 失败: {e}")

    def find_message_in_db(self, chat_id: str, msg_id: str) -> Optional[dict]:
        """从数据库中查找消息"""
        table_name = self.get_table_name(chat_id)
        try:
            cursor = self.db_connection.cursor()
            cursor.execute(f"""
                SELECT msg_id, sender_wxid, create_time, content, msg_type
                FROM "{table_name}"
                WHERE msg_id = ?
                ORDER BY create_time DESC
                LIMIT 1
            """, (msg_id,))

            row = cursor.fetchone()
            if row:
                return {
                    "msg_id": row[0],
                    "sender_wxid": row[1],
                    "create_time": row[2],
                    "content": row[3],
                    "msg_type": row[4]
                }
        except sqlite3.Error as e:
            logger.error(f"从表 {table_name} 查找消息失败: {e}")

        return None

    @on_text_message(priority=50)
    async def handle_text_message(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return True

        try:
            chat_id = message.get("FromWxid", "")
            msg_id = str(message.get("MsgId", ""))
            sender_wxid = message.get("SenderWxid", "")
            content = message.get("Content", "")
            create_time = message.get("CreateTime", 0)

            # 1. 创建消息表（如果不存在）
            self.create_message_table_if_not_exists(chat_id)

            # 2. 保存消息到数据库
            self.save_message_to_db(chat_id, msg_id, sender_wxid, create_time, content, msg_type=1)

            # 3. 检查是否为查询撤回消息的命令
            if any(trigger in content for trigger in self.trigger_words):
                await self.handle_recall_query(bot, message)
                return False  # 阻止后续插件处理

        except Exception as e:
            logger.error(f"处理文本消息时出错: {e}")

        return True

    async def process_message(self, bot: WechatAPIClient, message: dict):
        """重写基类的消息处理方法，用于捕获系统消息（撤回消息）"""
        if not self.enable:
            return True

        try:
            msg_type = message.get("MsgType", 0)
            content = message.get("Content", "")

            # 处理系统消息（撤回消息）
            if msg_type == 10002 and "revokemsg" in content and "sysmsg" in content:
                await self.handle_recall_message(bot, message, content)

        except Exception as e:
            logger.error(f"处理系统消息时出错: {e}")

        return True

    async def handle_recall_message(self, bot: WechatAPIClient, message: dict, xml_content: str):
        """处理撤回消息"""
        try:
            # 解析XML内容
            recall_info = self.parse_recall_xml(xml_content)
            if not recall_info:
                return

            chat_id = message.get("FromWxid", "")
            recall_time = message.get("CreateTime", 0)
            recaller_wxid = message.get("SenderWxid", "")

            logger.info(f"检测到撤回消息: chat_id={chat_id}, msgid={recall_info['msgid']}")

            # 从数据库中查找被撤回的原始消息
            original_msg = self.find_message_in_db(chat_id, recall_info["msgid"])

            if original_msg:
                # 获取发送者和撤回者的昵称
                sender_nickname = await self.get_user_nickname(bot, original_msg["sender_wxid"])
                recaller_nickname = await self.get_user_nickname(bot, recaller_wxid)

                # 保存撤回消息到数据库
                await self.save_recall_message(
                    chat_id=chat_id,
                    original_msg_id=recall_info["msgid"],
                    sender_wxid=original_msg["sender_wxid"],
                    sender_nickname=sender_nickname,
                    original_content=original_msg["content"],
                    original_msg_type=original_msg["msg_type"],
                    recall_time=recall_time,
                    recaller_wxid=recaller_wxid,
                    recaller_nickname=recaller_nickname
                )

                logger.info(f"记录撤回消息: {sender_nickname}({original_msg['sender_wxid']}) 的消息被 {recaller_nickname}({recaller_wxid}) 撤回")
                logger.info(f"撤回内容: {original_msg['content'][:50]}...")
            else:
                logger.warning(f"未找到被撤回的原始消息，消息ID: {recall_info['msgid']}, chat_id: {chat_id}")

        except Exception as e:
            logger.error(f"处理撤回消息时出错: {e}")

    def parse_recall_xml(self, xml_content: str) -> Optional[dict]:
        """解析撤回消息的XML内容"""
        try:
            # 清理XML内容
            xml_content = xml_content.strip()

            # 移除可能的用户名前缀（如 "flyhunterl:\n"）
            if ":\n" in xml_content and xml_content.index(":\n") < 50:
                xml_content = xml_content.split(":\n", 1)[1]

            root = ET.fromstring(xml_content)
            revoke_elem = root.find(".//revokemsg")

            if revoke_elem is not None:
                return {
                    "session": revoke_elem.find("session").text if revoke_elem.find("session") is not None else "",
                    "msgid": revoke_elem.find("msgid").text if revoke_elem.find("msgid") is not None else "",
                    "newmsgid": revoke_elem.find("newmsgid").text if revoke_elem.find("newmsgid") is not None else "",
                    "replacemsg": revoke_elem.find("replacemsg").text if revoke_elem.find("replacemsg") is not None else ""
                }
        except Exception as e:
            logger.error(f"解析撤回消息XML失败: {e}")
            logger.debug(f"XML内容: {xml_content}")

        return None

    async def get_user_nickname(self, bot: WechatAPIClient, wxid: str) -> str:
        """获取用户昵称"""
        try:
            if hasattr(bot, 'get_nickname'):
                nickname = await bot.get_nickname(wxid)
                return nickname if nickname else wxid
        except Exception as e:
            logger.debug(f"获取用户 {wxid} 昵称失败: {e}")

        return wxid

    async def save_recall_message(self, chat_id: str, original_msg_id: str, sender_wxid: str,
                                sender_nickname: str, original_content: str, original_msg_type: int,
                                recall_time: int, recaller_wxid: str, recaller_nickname: str):
        """保存撤回消息到数据库"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                INSERT INTO recall_messages
                (chat_id, original_msg_id, sender_wxid, sender_nickname, original_content,
                 original_msg_type, recall_time, recaller_wxid, recaller_nickname, created_at)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                chat_id, original_msg_id, sender_wxid, sender_nickname, original_content,
                original_msg_type, recall_time, recaller_wxid, recaller_nickname,
                int(datetime.now().timestamp())
            ))
            self.db_connection.commit()

            # 清理过期的撤回消息
            await self.cleanup_old_recalls(chat_id)

        except sqlite3.Error as e:
            logger.error(f"保存撤回消息失败: {e}")

    async def cleanup_old_recalls(self, chat_id: str):
        """清理过期的撤回消息"""
        try:
            cutoff_time = datetime.now() - timedelta(days=self.save_days)
            cutoff_timestamp = int(cutoff_time.timestamp())

            cursor = self.db_connection.cursor()
            cursor.execute("""
                DELETE FROM recall_messages
                WHERE chat_id = ? AND recall_time < ?
            """, (chat_id, cutoff_timestamp))

            # 限制每个聊天的撤回消息数量
            cursor.execute("""
                DELETE FROM recall_messages
                WHERE chat_id = ? AND id NOT IN (
                    SELECT id FROM recall_messages
                    WHERE chat_id = ?
                    ORDER BY recall_time DESC
                    LIMIT ?
                )
            """, (chat_id, chat_id, self.max_recall_messages))

            self.db_connection.commit()

        except sqlite3.Error as e:
            logger.error(f"清理过期撤回消息失败: {e}")

    async def handle_recall_query(self, bot: WechatAPIClient, message: dict):
        """处理撤回消息查询请求"""
        try:
            chat_id = message.get("FromWxid", "")

            # 从数据库获取最近的撤回消息
            recall_messages = await self.get_recent_recalls(chat_id, limit=10)

            if not recall_messages:
                await bot.send_text_message(chat_id, "最近没有撤回的消息记录。")
                return

            # 构建回复消息
            reply_text = "📝 最近的撤回消息记录：\n\n"

            for i, recall in enumerate(recall_messages, 1):
                sender_name = recall["sender_nickname"] or recall["sender_wxid"]
                content = recall["original_content"]

                # 限制内容长度
                if len(content) > 50:
                    content = content[:50] + "..."

                reply_text += f"{i}. {sender_name}: {content}\n"

                if self.show_recall_time:
                    recall_time = datetime.fromtimestamp(recall["recall_time"])
                    reply_text += f"   ⏰ 撤回时间: {recall_time.strftime('%m-%d %H:%M:%S')}\n"

                if self.show_recaller_info and recall["recaller_nickname"]:
                    recaller_name = recall["recaller_nickname"] or recall["recaller_wxid"]
                    if recaller_name != sender_name:
                        reply_text += f"   👤 撤回者: {recaller_name}\n"

                reply_text += "\n"

            reply_text += "💡 提示：只显示最近10条撤回记录"

            await bot.send_text_message(chat_id, reply_text)

        except Exception as e:
            logger.error(f"处理撤回消息查询时出错: {e}")
            await bot.send_text_message(chat_id, "查询撤回消息时出现错误，请稍后重试。")

    async def get_recent_recalls(self, chat_id: str, limit: int = 10) -> List[dict]:
        """获取最近的撤回消息"""
        try:
            cursor = self.db_connection.cursor()
            cursor.execute("""
                SELECT sender_wxid, sender_nickname, original_content, original_msg_type,
                       recall_time, recaller_wxid, recaller_nickname
                FROM recall_messages
                WHERE chat_id = ?
                ORDER BY recall_time DESC
                LIMIT ?
            """, (chat_id, limit))

            rows = cursor.fetchall()
            recalls = []

            for row in rows:
                recalls.append({
                    "sender_wxid": row[0],
                    "sender_nickname": row[1],
                    "original_content": row[2],
                    "original_msg_type": row[3],
                    "recall_time": row[4],
                    "recaller_wxid": row[5],
                    "recaller_nickname": row[6]
                })

            return recalls

        except sqlite3.Error as e:
            logger.error(f"获取撤回消息失败: {e}")
            return []

    async def clear_old_messages(self):
        """定期清理旧消息"""
        while True:
            await asyncio.sleep(60 * 60 * 24)  # 每天检查一次
            try:
                cutoff_time = datetime.now() - timedelta(days=self.save_days)
                cutoff_timestamp = int(cutoff_time.timestamp())

                cursor = self.db_connection.cursor()

                # 获取所有消息表名
                cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
                tables = [row[0] for row in cursor.fetchall() if row[0].startswith("chat_")]

                for table in tables:
                    try:
                        cursor.execute(f"""
                            DELETE FROM "{table}"
                            WHERE create_time < ?
                        """, (cutoff_timestamp,))
                        self.db_connection.commit()
                        logger.info(f"已清理表 {table} 中 {cutoff_timestamp} 之前的旧消息")
                    except sqlite3.Error as e:
                        logger.error(f"清理表 {table} 失败: {e}")

            except Exception as e:
                logger.error(f"清理旧消息失败: {e}")

    async def start(self):
        """启动插件时启动清理旧消息的任务"""
        if self.enable:
            asyncio.create_task(self.clear_old_messages())
            logger.info("RecallMessageQuery 插件启动，已启动定时清理任务")

    async def close(self):
        """插件关闭时的清理工作"""
        try:
            if self.db_connection:
                self.db_connection.close()
                logger.info("撤回消息数据库连接已关闭")
        except Exception as e:
            logger.error(f"关闭撤回消息插件时出错: {e}")