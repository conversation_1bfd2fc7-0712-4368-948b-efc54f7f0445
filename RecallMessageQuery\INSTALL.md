# 安装说明

## 快速安装

1. **复制插件文件夹**
   ```bash
   # 将整个 RecallMessageQuery 文件夹复制到 XYBotv2 的 plugins 目录下
   cp -r RecallMessageQuery /path/to/XYBotv2/plugins/
   ```

2. **安装依赖**
   ```bash
   # 如果是 Python 3.11+
   pip install loguru

   # 如果是 Python 3.10 或更早版本
   pip install loguru toml
   ```

3. **重启 XYBotv2**
   ```bash
   # 重启你的机器人程序
   ```

## 验证安装

1. **检查日志**
   启动后应该看到类似日志：
   ```
   RecallMessageQuery 插件配置加载成功
   撤回消息数据库连接已建立
   撤回消息表创建成功
   ```

2. **测试功能**
   - 在聊天中发送一条消息，然后撤回
   - 发送 `撤回了啥` 查询撤回记录

## 故障排除

### 插件未加载
- 检查文件夹是否在正确位置：`plugins/RecallMessageQuery/`
- 检查配置文件语法是否正确
- 查看启动日志中的错误信息

### 导入错误
- 确保安装了所需依赖：`pip install loguru toml`
- 检查 Python 版本兼容性

### 数据库错误
- 检查文件写入权限
- 确保 SQLite 可用

## 配置文件位置

```
plugins/
└── RecallMessageQuery/
    ├── __init__.py
    ├── main.py
    ├── config.toml    # 在这里修改配置
    └── README.md
```

## 默认配置

```toml
[basic]
enable = true
priority = 60

[RecallMessageQuery]
trigger_words = ["撤回了啥", "撤回的什么", "撤回了什么", "看看撤回", "撤回查询"]
max_recall_messages = 50
save_days = 7
show_recall_time = true
show_recaller_info = true
```
