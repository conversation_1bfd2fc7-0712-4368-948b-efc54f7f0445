# RecallMessageQuery - XYBotv2 撤回消息查询插件 🔍

## 简介

`RecallMessageQuery` 是一款强大的撤回消息查询插件！它可以自动记录被撤回的消息内容，并提供便捷的查询功能，让你再也不会错过任何重要的撤回消息 🕵️‍♂️。

## 功能特性

- **🔍 自动记录撤回消息**：实时监控并记录所有被撤回的消息内容
- **📝 智能查询**：通过简单的关键词即可查询最近的撤回消息
- **👤 用户信息显示**：显示撤回消息的发送者和撤回者信息
- **⏰ 时间记录**：准确记录消息撤回的时间
- **💾 数据库存储**：使用SQLite数据库持久化存储撤回消息
- **🧹 自动清理**：定期清理过期的撤回消息，保持数据库精简
- **⚙️ 灵活配置**：支持多种配置选项，满足不同需求

## 激活关键词

- `撤回了啥`
- `撤回的什么`
- `撤回了什么`
- `看看撤回`
- `撤回查询`



插件的配置位于 `config.toml` 文件中。以下是配置示例：

```toml
[basic]
# 是否启用插件
enable = true
# 全局优先级设置 (0-99)，值越高优先级越高
priority = 60

[RecallMessageQuery]
# 触发查询撤回消息的关键词
trigger_words = ["撤回了啥", "撤回的什么", "撤回了什么", "看看撤回", "撤回查询"]
# 最大存储撤回消息数量（每个聊天）
max_recall_messages = 50
# 撤回消息保存天数
save_days = 7
# 是否显示撤回时间
show_recall_time = true
# 是否显示撤回者信息
show_recaller_info = true
```

### 配置说明

- **enable**: 是否启用插件
- **priority**: 插件优先级，数值越高优先级越高
- **trigger_words**: 触发查询的关键词列表
- **max_recall_messages**: 每个聊天最多保存的撤回消息数量
- **save_days**: 撤回消息保存天数，超过此时间的消息会被自动清理
- **show_recall_time**: 是否在查询结果中显示撤回时间
- **show_recaller_info**: 是否显示撤回者信息（当撤回者与发送者不同时）

## 使用方法

### 查询撤回消息

在任何聊天中发送以下任一关键词：

- `撤回了啥`
- `撤回的什么`
- `撤回了什么`
- `看看撤回`
- `撤回查询`

插件会返回最近10条撤回消息的记录，包括：

- 发送者昵称
- 撤回的消息内容（超过50字符会截断）
- 撤回时间
- 撤回者信息（如果与发送者不同）

### 示例输出

```
📝 最近的撤回消息记录：

1. 张三: 这是一条被撤回的消息...
   ⏰ 撤回时间: 06-01 10:18:42

2. 李四: 哈哈哈，刚才说错了
   ⏰ 撤回时间: 06-01 09:45:23
   👤 撤回者: 群主

3. 王五: 发错群了，不好意思
   ⏰ 撤回时间: 06-01 09:30:15

💡 提示：只显示最近10条撤回记录
```

## 工作原理

1. **消息存储**：插件监控所有文本消息，并将其保存到SQLite数据库中（每个聊天独立表）
2. **撤回检测**：当检测到撤回消息的XML系统消息时，解析撤回信息
3. **消息匹配**：根据消息ID从数据库中精确查找被撤回的原始消息
4. **撤回记录**：将撤回消息的详细信息保存到专门的撤回记录表
5. **查询响应**：当用户发送查询关键词时，从数据库中检索并返回结果

### 数据库设计

- **消息表**：每个聊天创建独立表 `chat_[聊天ID]`，存储所有消息
- **撤回记录表**：全局表 `recall_messages`，存储所有撤回记录
- **自动清理**：定期清理过期的消息和撤回记录

## 数据库结构

插件使用SQLite数据库存储撤回消息，表结构如下：

```sql
CREATE TABLE recall_messages (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    chat_id TEXT NOT NULL,              -- 聊天ID
    original_msg_id TEXT NOT NULL,      -- 原始消息ID
    sender_wxid TEXT NOT NULL,          -- 发送者微信ID
    sender_nickname TEXT,               -- 发送者昵称
    original_content TEXT NOT NULL,     -- 原始消息内容
    original_msg_type INTEGER,          -- 原始消息类型
    recall_time INTEGER NOT NULL,       -- 撤回时间戳
    recaller_wxid TEXT,                 -- 撤回者微信ID
    recaller_nickname TEXT,             -- 撤回者昵称
    created_at INTEGER NOT NULL        -- 记录创建时间
);
```

## 注意事项

- 插件只能记录在其运行期间发生的撤回消息
- 消息内容在内存中的保存时间有限（最近100条消息）
- 数据库会定期清理过期的撤回记录
- 插件需要适当的权限来监控消息和访问用户信息

## 更新日志

### v1.0.0
- 初始版本发布
- 支持撤回消息的自动记录和查询
- 支持多种触发关键词
- 支持数据库持久化存储
- 支持自动清理过期数据

## 贡献

欢迎提交Issue和Pull Request来改进这个插件！

## 许可证

本项目采用MIT许可证。

---

**给个 ⭐ Star 支持吧！** 😊
